<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Doughnut, Bar, Line } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement
);

interface Props {
  projectId: string;
}

const props = defineProps<Props>();
const { getProjectTestCaseAnalyticsWithFallback, loading, error } = useReportData();

const testCaseData = ref<any>(null);

// Chart options
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
};

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const automationCoverageData = ref({
  labels: ['Manual', 'Automation'],
  datasets: [
    {
      data: [0, 0],
      backgroundColor: ['#ef4444', '#10b981'],
      borderWidth: 0,
    },
  ],
});

const typeDistributionData = ref({
  labels: [],
  datasets: [
    {
      data: [],
      backgroundColor: ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#10b981'],
      borderWidth: 0,
    },
  ],
});

const priorityDistributionData = ref({
  labels: [],
  datasets: [
    {
      data: [],
      backgroundColor: ['#ef4444', '#f59e0b', '#3b82f6', '#10b981'],
      borderWidth: 0,
    },
  ],
});

const platformDistributionData = ref({
  labels: [],
  datasets: [
    {
      data: [],
      backgroundColor: ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444'],
      borderWidth: 0,
    },
  ],
});

const growthOverTimeData = ref({
  labels: [],
  datasets: [
    {
      label: 'Test Cases Created',
      data: [],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
});

const fetchData = async () => {
  try {
    const analytics = await getProjectTestCaseAnalyticsWithFallback(props.projectId);
    testCaseData.value = analytics;

    // Update automation coverage
    if (analytics.automationCoverage) {
      const manual = analytics.automationCoverage.find((item: any) => item.type === 'manual')?.count || 0;
      const automation = analytics.automationCoverage.find((item: any) => item.type === 'automation')?.count || 0;
      
      automationCoverageData.value = {
        labels: ['Manual', 'Automation'],
        datasets: [
          {
            data: [parseInt(manual), parseInt(automation)],
            backgroundColor: ['#ef4444', '#10b981'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update type distribution
    if (analytics.typeDistribution) {
      typeDistributionData.value = {
        labels: analytics.typeDistribution.map((item: any) => item.type),
        datasets: [
          {
            data: analytics.typeDistribution.map((item: any) => parseInt(item.count)),
            backgroundColor: ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#10b981'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update priority distribution
    if (analytics.priorityDistribution) {
      const priorityOrder = ['critical', 'high', 'medium', 'low'];
      const sortedPriorities = priorityOrder.map(priority => 
        analytics.priorityDistribution.find((item: any) => item.priority === priority)
      ).filter(Boolean);

      priorityDistributionData.value = {
        labels: sortedPriorities.map((item: any) => item.priority),
        datasets: [
          {
            data: sortedPriorities.map((item: any) => parseInt(item.count)),
            backgroundColor: ['#ef4444', '#f59e0b', '#3b82f6', '#10b981'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update platform distribution
    if (analytics.platformDistribution) {
      platformDistributionData.value = {
        labels: analytics.platformDistribution.map((item: any) => item.platform),
        datasets: [
          {
            data: analytics.platformDistribution.map((item: any) => parseInt(item.count)),
            backgroundColor: ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update growth over time
    if (analytics.growthOverTime) {
      const sortedGrowth = analytics.growthOverTime.sort((a: any, b: any) => 
        new Date(a.month).getTime() - new Date(b.month).getTime()
      );
      
      growthOverTimeData.value = {
        labels: sortedGrowth.map((item: any) => 
          new Date(item.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
        ),
        datasets: [
          {
            label: 'Test Cases Created',
            data: sortedGrowth.map((item: any) => parseInt(item.count)),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch test case analytics:', err);
  }
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="test-case-analytics">
    <div class="analytics-header">
      <h2>Test Case Analytics</h2>
      <p>Comprehensive analysis of test case distribution and automation coverage</p>
    </div>

    <div v-if="loading" class="loading">
      Loading test case analytics...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="analytics-content">
      <div class="analytics-grid">
        <!-- Automation Coverage -->
        <div class="chart-card">
          <h3>Automation Coverage</h3>
          <div class="chart-container">
            <Doughnut :data="automationCoverageData" :options="doughnutOptions" />
          </div>
          <div class="chart-summary">
            <div class="summary-item">
              <span class="summary-label">Automation Rate:</span>
              <span class="summary-value">
                {{ 
                  testCaseData?.automationCoverage ? 
                  Math.round((parseInt(testCaseData.automationCoverage.find((item: any) => item.type === 'automation')?.count || '0') / 
                  testCaseData.automationCoverage.reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0 
                }}%
              </span>
            </div>
          </div>
        </div>

        <!-- Type Distribution -->
        <div class="chart-card">
          <h3>Test Case Types</h3>
          <div class="chart-container">
            <Doughnut :data="typeDistributionData" :options="doughnutOptions" />
          </div>
        </div>

        <!-- Priority Distribution -->
        <div class="chart-card">
          <h3>Priority Distribution</h3>
          <div class="chart-container">
            <Doughnut :data="priorityDistributionData" :options="doughnutOptions" />
          </div>
        </div>

        <!-- Platform Distribution -->
        <div class="chart-card">
          <h3>Platform Distribution</h3>
          <div class="chart-container">
            <Doughnut :data="platformDistributionData" :options="doughnutOptions" />
          </div>
        </div>

        <!-- Growth Over Time -->
        <div class="chart-card full-width">
          <h3>Test Case Growth Over Time</h3>
          <div class="chart-container">
            <Line :data="growthOverTimeData" :options="lineOptions" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.test-case-analytics {
  .analytics-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 8px 0;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .summary-label {
    font-size: 14px;
    color: #6b7280;
  }

  .summary-value {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

@media (max-width: 768px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
