<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Bar, Line } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement
);

interface Props {
  projectId: string;
}

const props = defineProps<Props>();
const { getProjectTestRunPerformanceWithFallback, loading, error } = useReportData();

const performanceData = ref<any>(null);

// Chart options
const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
    x: {
      stacked: true,
    },
    y: {
      stacked: true,
    },
  },
};

const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const passFail = ref({
  labels: [],
  datasets: [
    {
      label: 'Passed',
      data: [],
      backgroundColor: '#10b981',
    },
    {
      label: 'Failed',
      data: [],
      backgroundColor: '#ef4444',
    },
    {
      label: 'Skipped',
      data: [],
      backgroundColor: '#f59e0b',
    },
  ],
});

const executionTimeData = ref({
  labels: [],
  datasets: [
    {
      label: 'Average Execution Time (seconds)',
      data: [],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
});

const fetchData = async () => {
  try {
    const performance = await getProjectTestRunPerformanceWithFallback(props.projectId);
    performanceData.value = performance;

    // Update pass/fail chart
    if (performance.passFail) {
      const testRuns = performance.passFail.reduce((acc: any, item: any) => {
        if (!acc[item.testRunName]) {
          acc[item.testRunName] = { passed: 0, failed: 0, skipped: 0, blocked: 0, untested: 0 };
        }
        acc[item.testRunName][item.status] = parseInt(item.count);
        return acc;
      }, {});

      const testRunNames = Object.keys(testRuns).slice(0, 10); // Show last 10 test runs
      
      passFail.value = {
        labels: testRunNames,
        datasets: [
          {
            label: 'Passed',
            data: testRunNames.map(name => testRuns[name].passed || 0),
            backgroundColor: '#10b981',
          },
          {
            label: 'Failed',
            data: testRunNames.map(name => testRuns[name].failed || 0),
            backgroundColor: '#ef4444',
          },
          {
            label: 'Skipped',
            data: testRunNames.map(name => testRuns[name].skipped || 0),
            backgroundColor: '#f59e0b',
          },
          {
            label: 'Blocked',
            data: testRunNames.map(name => testRuns[name].blocked || 0),
            backgroundColor: '#8b5cf6',
          },
          {
            label: 'Untested',
            data: testRunNames.map(name => testRuns[name].untested || 0),
            backgroundColor: '#6b7280',
          },
        ],
      };
    }

    // Update execution time chart
    if (performance.executionTime) {
      executionTimeData.value = {
        labels: performance.executionTime.map((item: any) => item.testRunName),
        datasets: [
          {
            label: 'Average Execution Time (seconds)',
            data: performance.executionTime.map((item: any) => parseFloat(item.avgDuration)),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch test run performance data:', err);
  }
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="test-run-performance">
    <div class="performance-header">
      <h2>Test Run Performance</h2>
      <p>Analysis of test execution results and performance trends</p>
    </div>

    <div v-if="loading" class="loading">
      Loading test run performance data...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="performance-content">
      <div class="performance-grid">
        <!-- Pass/Fail Trends -->
        <div class="chart-card full-width">
          <h3>Pass/Fail Trends (Recent Test Runs)</h3>
          <div class="chart-container">
            <Bar :data="passFail" :options="barOptions" />
          </div>
        </div>

        <!-- Execution Time Analysis -->
        <div class="chart-card">
          <h3>Execution Time Analysis</h3>
          <div class="chart-container">
            <Line :data="executionTimeData" :options="lineOptions" />
          </div>
        </div>

        <!-- Flaky Tests -->
        <div class="chart-card">
          <h3>Flaky Test Identification</h3>
          <div class="flaky-tests-list">
            <div v-if="performanceData?.flakyTests?.length > 0">
              <div 
                v-for="test in performanceData.flakyTests" 
                :key="test.testCaseTitle"
                class="flaky-test-item"
              >
                <div class="test-title">{{ test.testCaseTitle }}</div>
                <div class="test-stats">
                  <span class="stat">{{ test.statusCount }} different statuses</span>
                  <span class="stat">{{ test.totalRuns }} total runs</span>
                </div>
              </div>
            </div>
            <div v-else class="no-flaky-tests">
              <span class="icon">✅</span>
              <span>No flaky tests detected</span>
            </div>
          </div>
        </div>

        <!-- Performance Summary -->
        <div class="chart-card">
          <h3>Performance Summary</h3>
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-value">
                {{ performanceData?.executionTime?.length || 0 }}
              </div>
              <div class="stat-label">Test Runs Analyzed</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">
                {{ 
                  performanceData?.executionTime?.length > 0 ? 
                  Math.round(performanceData.executionTime.reduce((sum: number, item: any) => sum + parseFloat(item.avgDuration), 0) / performanceData.executionTime.length) : 0 
                }}s
              </div>
              <div class="stat-label">Avg Execution Time</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">
                {{ performanceData?.flakyTests?.length || 0 }}
              </div>
              <div class="stat-label">Flaky Tests</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.test-run-performance {
  .performance-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 8px 0;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 300px;
  position: relative;
}

.flaky-tests-list {
  max-height: 300px;
  overflow-y: auto;
}

.flaky-test-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;

  .test-title {
    font-weight: 500;
    color: #111827;
    margin-bottom: 4px;
  }

  .test-stats {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #6b7280;
  }
}

.no-flaky-tests {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: #10b981;
  font-weight: 500;

  .icon {
    font-size: 20px;
  }
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;

  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

@media (max-width: 768px) {
  .performance-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
