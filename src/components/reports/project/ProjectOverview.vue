<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Doughnut, Line } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement
);

interface Props {
  projectId: string;
}

const props = defineProps<Props>();
const { getProjectTestCaseAnalyticsWithFallback, getProjectTestRunPerformanceWithFallback, loading, error } = useReportData();

const testCaseData = ref<any>(null);
const testRunData = ref<any>(null);

// Chart options
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
};

const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Automation coverage chart data
const automationCoverageData = ref({
  labels: ['Manual', 'Automation'],
  datasets: [
    {
      data: [0, 0],
      backgroundColor: ['#ef4444', '#10b981'],
      borderWidth: 0,
    },
  ],
});

// Test case growth chart data
const testCaseGrowthData = ref({
  labels: [],
  datasets: [
    {
      label: 'Test Cases Created',
      data: [],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
});

// Recent test run results
const recentTestRunsData = ref({
  labels: [],
  datasets: [
    {
      label: 'Passed',
      data: [],
      backgroundColor: '#10b981',
    },
    {
      label: 'Failed',
      data: [],
      backgroundColor: '#ef4444',
    },
    {
      label: 'Skipped',
      data: [],
      backgroundColor: '#f59e0b',
    },
  ],
});

const fetchData = async () => {
  try {
    // Fetch test case analytics
    const testCaseAnalytics = await getProjectTestCaseAnalyticsWithFallback(props.projectId);
    testCaseData.value = testCaseAnalytics;

    // Update automation coverage chart
    if (testCaseAnalytics.automationCoverage) {
      const manual = testCaseAnalytics.automationCoverage.find((item: any) => item.type === 'manual')?.count || 0;
      const automation = testCaseAnalytics.automationCoverage.find((item: any) => item.type === 'automation')?.count || 0;
      
      automationCoverageData.value = {
        labels: ['Manual', 'Automation'],
        datasets: [
          {
            data: [manual, automation],
            backgroundColor: ['#ef4444', '#10b981'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update test case growth chart
    if (testCaseAnalytics.growthOverTime) {
      const sortedGrowth = testCaseAnalytics.growthOverTime.sort((a: any, b: any) => 
        new Date(a.month).getTime() - new Date(b.month).getTime()
      );
      
      testCaseGrowthData.value = {
        labels: sortedGrowth.map((item: any) => 
          new Date(item.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
        ),
        datasets: [
          {
            label: 'Test Cases Created',
            data: sortedGrowth.map((item: any) => parseInt(item.count)),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
          },
        ],
      };
    }

    // Fetch test run performance
    const testRunPerformance = await getProjectTestRunPerformanceWithFallback(props.projectId);
    testRunData.value = testRunPerformance;

    // Update recent test runs chart
    if (testRunPerformance.passFail) {
      const testRuns = testRunPerformance.passFail.reduce((acc: any, item: any) => {
        if (!acc[item.testRunName]) {
          acc[item.testRunName] = { passed: 0, failed: 0, skipped: 0 };
        }
        acc[item.testRunName][item.status] = parseInt(item.count);
        return acc;
      }, {});

      const testRunNames = Object.keys(testRuns).slice(0, 5); // Show last 5 test runs
      
      recentTestRunsData.value = {
        labels: testRunNames,
        datasets: [
          {
            label: 'Passed',
            data: testRunNames.map(name => testRuns[name].passed || 0),
            backgroundColor: '#10b981',
          },
          {
            label: 'Failed',
            data: testRunNames.map(name => testRuns[name].failed || 0),
            backgroundColor: '#ef4444',
          },
          {
            label: 'Skipped',
            data: testRunNames.map(name => testRuns[name].skipped || 0),
            backgroundColor: '#f59e0b',
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch project overview data:', err);
  }
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="project-overview">
    <div v-if="loading" class="loading">
      Loading project overview...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="overview-content">
      <div class="overview-grid">
        <!-- Automation Coverage -->
        <div class="chart-card">
          <h3>Automation Coverage</h3>
          <div class="chart-container">
            <Doughnut :data="automationCoverageData" :options="doughnutOptions" />
          </div>
        </div>

        <!-- Test Case Growth -->
        <div class="chart-card">
          <h3>Test Case Growth (Last 6 Months)</h3>
          <div class="chart-container">
            <Line :data="testCaseGrowthData" :options="lineOptions" />
          </div>
        </div>

        <!-- Recent Test Runs -->
        <div class="chart-card">
          <h3>Recent Test Run Results</h3>
          <div class="chart-container">
            <Line :data="recentTestRunsData" :options="lineOptions" />
          </div>
        </div>

        <!-- Summary Stats -->
        <div class="stats-card">
          <h3>Quick Stats</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">
                {{ testCaseData?.automationCoverage?.reduce((sum: number, item: any) => sum + parseInt(item.count), 0) || 0 }}
              </div>
              <div class="stat-label">Total Test Cases</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">
                {{ testCaseData?.automationCoverage?.find((item: any) => item.type === 'automation')?.count || 0 }}
              </div>
              <div class="stat-label">Automated</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">
                {{ testRunData?.flakyTests?.length || 0 }}
              </div>
              <div class="stat-label">Flaky Tests</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.project-overview {
  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-card, .stats-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 300px;
  position: relative;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;

  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
