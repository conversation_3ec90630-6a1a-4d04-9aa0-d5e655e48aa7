import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestCase } from '../test-cases/test-case.entity';
import { TestRun } from '../test-runs/test-run.entity';
import { TestResult } from '../test-results/test-result.entity';
import { Project } from '../projects/project.entity';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(TestCase)
    private testCaseRepository: Repository<TestCase>,
    @InjectRepository(TestRun)
    private testRunRepository: Repository<TestRun>,
    @InjectRepository(TestResult)
    private testResultRepository: Repository<TestResult>,
    @InjectRepository(Project)
    private projectRepository: Repository<Project>,
  ) {}

  // Helper method to verify project access
  private async verifyProjectAccess(projectId: string, userId: string): Promise<void> {
    const project = await this.projectRepository
      .createQueryBuilder('project')
      .leftJoin('project.company', 'company')
      .leftJoin('company.users', 'user')
      .where('project.id = :projectId', { projectId })
      .andWhere('user.id = :userId', { userId })
      .getOne();

    if (!project) {
      throw new NotFoundException('Project not found or access denied');
    }
  }

  // Project-specific analytics
  async getProjectTestCaseAnalytics(projectId: string, userId: string) {
    await this.verifyProjectAccess(projectId, userId);

    // Automation coverage
    const automationCoverage = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('tc.testCaseType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId = :projectId', { projectId })
      .groupBy('tc.testCaseType')
      .getRawMany();

    // Distribution by type
    const typeDistribution = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('tc.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId = :projectId', { projectId })
      .groupBy('tc.type')
      .getRawMany();

    // Distribution by priority
    const priorityDistribution = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('tc.priority', 'priority')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId = :projectId', { projectId })
      .groupBy('tc.priority')
      .getRawMany();

    // Distribution by platform
    const platformDistribution = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('tc.platform', 'platform')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId = :projectId', { projectId })
      .groupBy('tc.platform')
      .getRawMany();

    // Test case growth over time (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const growthOverTime = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select("DATE_TRUNC('month', tc.createdAt)", 'month')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId = :projectId', { projectId })
      .andWhere('tc.createdAt >= :sixMonthsAgo', { sixMonthsAgo })
      .groupBy("DATE_TRUNC('month', tc.createdAt)")
      .orderBy("DATE_TRUNC('month', tc.createdAt)", 'ASC')
      .getRawMany();

    return {
      automationCoverage,
      typeDistribution,
      priorityDistribution,
      platformDistribution,
      growthOverTime
    };
  }

  async getProjectTestRunPerformance(projectId: string, userId: string) {
    await this.verifyProjectAccess(projectId, userId);

    // Get recent test runs (last 10)
    const recentTestRuns = await this.testRunRepository
      .createQueryBuilder('tr')
      .select(['tr.id', 'tr.name', 'tr.createdAt'])
      .where('tr.projectId = :projectId', { projectId })
      .orderBy('tr.createdAt', 'DESC')
      .limit(10)
      .getMany();

    const testRunIds = recentTestRuns.map(tr => tr.id);

    if (testRunIds.length === 0) {
      return { passFail: [], executionTime: [], flakyTests: [] };
    }

    // Pass/fail data for recent test runs
    const passFail = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testRun', 'testRun')
      .select('testRun.id', 'testRunId')
      .addSelect('testRun.name', 'testRunName')
      .addSelect('testRun.createdAt', 'createdAt')
      .addSelect('result.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('result.testRunId IN (:...testRunIds)', { testRunIds })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .groupBy('testRun.id, testRun.name, testRun.createdAt, result.status')
      .orderBy('testRun.createdAt', 'DESC')
      .getRawMany();

    // Execution time analysis
    const executionTime = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testRun', 'testRun')
      .select('testRun.id', 'testRunId')
      .addSelect('testRun.name', 'testRunName')
      .addSelect('AVG(result.duration)', 'avgDuration')
      .addSelect('MAX(result.duration)', 'maxDuration')
      .addSelect('MIN(result.duration)', 'minDuration')
      .where('result.testRunId IN (:...testRunIds)', { testRunIds })
      .andWhere('result.duration IS NOT NULL')
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .groupBy('testRun.id, testRun.name')
      .getRawMany();

    // Flaky test identification
    const flakyTests = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testCase', 'testCase')
      .select('testCase.id', 'testCaseId')
      .addSelect('testCase.title', 'testCaseTitle')
      .addSelect('COUNT(DISTINCT result.status)', 'statusCount')
      .addSelect('COUNT(*)', 'totalRuns')
      .where('result.testRunId IN (:...testRunIds)', { testRunIds })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .andWhere('result.status IN (:...statuses)', { statuses: ['passed', 'failed'] })
      .groupBy('testCase.id, testCase.title')
      .having('COUNT(DISTINCT result.status) > 1')
      .getRawMany();

    return {
      passFail,
      executionTime,
      flakyTests
    };
  }

  async getProjectExecutionEfficiency(projectId: string, userId: string) {
    await this.verifyProjectAccess(projectId, userId);

    // Automation statistics
    const automationStats = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('tc.testCaseType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId = :projectId', { projectId })
      .groupBy('tc.testCaseType')
      .getRawMany();

    // Test execution frequency
    const executionFrequency = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testCase', 'testCase')
      .leftJoin('result.testRun', 'testRun')
      .select('testCase.id', 'testCaseId')
      .addSelect('testCase.title', 'testCaseTitle')
      .addSelect('testCase.testCaseType', 'testCaseType')
      .addSelect('COUNT(*)', 'executionCount')
      .where('testRun.projectId = :projectId', { projectId })
      .groupBy('testCase.id, testCase.title, testCase.testCaseType')
      .orderBy('COUNT(*)', 'DESC')
      .limit(20)
      .getRawMany();

    return {
      automationStats,
      executionFrequency
    };
  }

  // Cross-project analytics (organization-wide)
  async getCrossProjectQualityComparison(userId: string) {
    // Get user's accessible projects
    const userProjects = await this.projectRepository
      .createQueryBuilder('project')
      .leftJoin('project.company', 'company')
      .leftJoin('company.users', 'user')
      .where('user.id = :userId', { userId })
      .getMany();

    const projectIds = userProjects.map(p => p.id);

    if (projectIds.length === 0) {
      return { projectCoverage: [], projectPassRates: [] };
    }

    // Test coverage by project
    const projectCoverage = await this.projectRepository
      .createQueryBuilder('project')
      .leftJoin('project.testCases', 'testCase')
      .select('project.id', 'projectId')
      .addSelect('project.name', 'projectName')
      .addSelect('COUNT(testCase.id)', 'totalTestCases')
      .addSelect('SUM(CASE WHEN testCase.testCaseType = \'automation\' THEN 1 ELSE 0 END)', 'automatedTestCases')
      .where('project.id IN (:...projectIds)', { projectIds })
      .groupBy('project.id, project.name')
      .getRawMany();

    // Pass rates across projects (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const projectPassRates = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testRun', 'testRun')
      .leftJoin('testRun.project', 'project')
      .select('project.id', 'projectId')
      .addSelect('project.name', 'projectName')
      .addSelect('COUNT(*)', 'totalTests')
      .addSelect('SUM(CASE WHEN result.status = \'passed\' THEN 1 ELSE 0 END)', 'passedTests')
      .where('result.createdAt >= :thirtyDaysAgo', { thirtyDaysAgo })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .andWhere('project.id IN (:...projectIds)', { projectIds })
      .groupBy('project.id, project.name')
      .getRawMany();

    return {
      projectCoverage,
      projectPassRates
    };
  }

  async getOrganizationTestingHealth(userId: string) {
    // Get user's accessible projects
    const userProjects = await this.projectRepository
      .createQueryBuilder('project')
      .leftJoin('project.company', 'company')
      .leftJoin('company.users', 'user')
      .where('user.id = :userId', { userId })
      .getMany();

    const projectIds = userProjects.map(p => p.id);

    if (projectIds.length === 0) {
      return { overallAutomation: [], testingVelocity: [], qualityTrends: [] };
    }

    // Overall automation adoption
    const overallAutomation = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('tc.testCaseType', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId IN (:...projectIds)', { projectIds })
      .groupBy('tc.testCaseType')
      .getRawMany();

    // Testing velocity (last 3 months)
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    const testingVelocity = await this.testRunRepository
      .createQueryBuilder('tr')
      .leftJoin('tr.project', 'project')
      .select('project.id', 'projectId')
      .addSelect('project.name', 'projectName')
      .addSelect("DATE_TRUNC('month', tr.createdAt)", 'month')
      .addSelect('COUNT(*)', 'testRunCount')
      .where('tr.createdAt >= :threeMonthsAgo', { threeMonthsAgo })
      .andWhere('project.id IN (:...projectIds)', { projectIds })
      .groupBy('project.id, project.name, DATE_TRUNC(\'month\', tr.createdAt)')
      .orderBy('DATE_TRUNC(\'month\', tr.createdAt)', 'DESC')
      .getRawMany();

    // Quality trends (last 8 weeks)
    const eightWeeksAgo = new Date();
    eightWeeksAgo.setDate(eightWeeksAgo.getDate() - 56);

    const qualityTrends = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testRun', 'testRun')
      .leftJoin('testRun.project', 'project')
      .select("DATE_TRUNC('week', result.createdAt)", 'week')
      .addSelect('COUNT(*)', 'totalTests')
      .addSelect('SUM(CASE WHEN result.status = \'passed\' THEN 1 ELSE 0 END)', 'passedTests')
      .where('result.createdAt >= :eightWeeksAgo', { eightWeeksAgo })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .andWhere('project.id IN (:...projectIds)', { projectIds })
      .groupBy("DATE_TRUNC('week', result.createdAt)")
      .orderBy("DATE_TRUNC('week', result.createdAt)", 'ASC')
      .getRawMany();

    return {
      overallAutomation,
      testingVelocity,
      qualityTrends
    };
  }

  async getAITestingImpact(userId: string) {
    // Get user's accessible projects
    const userProjects = await this.projectRepository
      .createQueryBuilder('project')
      .leftJoin('project.company', 'company')
      .leftJoin('company.users', 'user')
      .where('user.id = :userId', { userId })
      .getMany();

    const projectIds = userProjects.map(p => p.id);

    if (projectIds.length === 0) {
      return { aiTestCases: [], aiVsManualResults: [] };
    }

    // AI-generated test case statistics
    const aiTestCases = await this.testCaseRepository
      .createQueryBuilder('tc')
      .select('COALESCE(tc.automationByAgentq, false)', 'isAIGenerated')
      .addSelect('COUNT(*)', 'count')
      .where('tc.projectId IN (:...projectIds)', { projectIds })
      .groupBy('COALESCE(tc.automationByAgentq, false)')
      .getRawMany();

    // AI vs manual testing comparison
    const aiVsManualResults = await this.testResultRepository
      .createQueryBuilder('result')
      .leftJoin('result.testCase', 'testCase')
      .leftJoin('result.testRun', 'testRun')
      .leftJoin('testRun.project', 'project')
      .select('COALESCE(testCase.automationByAgentq, false)', 'isAIGenerated')
      .addSelect('result.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('result.isLatest = :isLatest', { isLatest: true })
      .andWhere('project.id IN (:...projectIds)', { projectIds })
      .groupBy('COALESCE(testCase.automationByAgentq, false), result.status')
      .getRawMany();

    return {
      aiTestCases,
      aiVsManualResults
    };
  }
}
