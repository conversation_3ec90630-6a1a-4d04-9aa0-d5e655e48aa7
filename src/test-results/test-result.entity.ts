import { Entity, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TestRun } from '../test-runs/test-run.entity';
import { TestCase } from '../test-cases/test-case.entity';

export enum TestResultStatus {
  PASSED = 'passed',
  FAILED = 'failed',
  BLOCKED = 'blocked',
  SKIPPED = 'skipped',
  UNTESTED = 'untested'
}

@Entity('test_results')
export class TestResult {
  @ApiProperty({
    description: 'The unique identifier of the test result',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => TestRun, { onDelete: 'CASCADE' })
  testRun: TestRun;

  @Column()
  testRunId: string;

  @ManyToOne(() => TestCase, { onDelete: 'CASCADE' })
  testCase: TestCase;

  @Column()
  testCaseId: string;

  @ApiProperty({
    description: 'Test result status',
    enum: TestResultStatus,
    example: TestResultStatus.PASSED
  })
  @Column({
    type: 'enum',
    enum: TestResultStatus,
    default: TestResultStatus.UNTESTED
  })
  status: TestResultStatus;

  @ApiProperty({
    description: 'Actual test result',
    example: 'User was successfully redirected to dashboard'
  })
  @Column('text', { nullable: true })
  actualResult: string;

  @ApiProperty({
    description: 'Test execution duration in milliseconds',
    example: 15000
  })
  @Column('int', { nullable: true })
  duration: number;

  @ApiProperty({
    description: 'Additional notes',
    example: 'Test failed due to network timeout'
  })
  @Column('text', { nullable: true })
  notes: string;

  @ApiProperty({
    description: 'Screenshot URL',
    example: 'https://storage.example.com/screenshots/test-123.png'
  })
  @Column({ nullable: true })
  screenshotUrl: string;

  @ApiProperty({
    description: 'Video recording URL',
    example: 'https://storage.example.com/videos/test-123.mp4'
  })
  @Column({ nullable: true })
  videoUrl: string;

  @ApiProperty({
    description: 'Google Cloud Storage URL for full logs',
    example: 'gs://agentq-test-logs/test-123/logs.json'
  })
  @Column({ nullable: true })
  logsUrl: string;

  @ApiProperty({
    description: 'Sequence number of this result within the test case and test run',
    example: 1
  })
  @Column({ default: 1 })
  sequence: number;

  @ApiProperty({
    description: 'Reference to the previous test result for this test case in this test run',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  previousResultId: string;

  @ManyToOne(() => TestResult, { nullable: true })
  previousResult: TestResult;

  @OneToMany(() => TestResult, testResult => testResult.previousResult)
  nextResults: TestResult[];

  @ApiProperty({
    description: 'Whether this is the latest result for this test case in this test run',
    example: true
  })
  @Column({ default: true })
  isLatest: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The name or email of the user who created this test result',
    example: '<EMAIL>'
  })
  @Column({ nullable: true })
  createdBy: string;
}
