import { Controller, Post, Body, Param, Get, Delete, UseGuards, Query, HttpException, HttpStatus, NotFoundException, UseInterceptors, UploadedFile, Patch, Request } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { File as MulterFile } from 'multer';
import { TestRunsService } from '../test-runs/test-runs.service';
import { TestResultsService } from './test-results.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { StorageService } from './storage.service';
import { Express } from "express";

@ApiTags('test-results')
@Controller()
export class TestResultsController {
  constructor(
    private readonly testResultsService: TestResultsService, 
    private readonly StorageService: StorageService,
    private readonly testRunsService: TestRunsService,
  ) {}

  @Post('test-results')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new test result (simple, no project/testRun required)' })
  @ApiResponse({ status: 201, description: 'Test result created successfully' })
  async createSimpleTestResult(
    @Body() createDto: { testRunId: string; testCaseId: string; status?: string },
    @Request() req
  ) {
    const createdBy = req.user?.name || req.user?.email || 'unknown';
    return this.testResultsService.createSimpleTestResult({ ...createDto, createdBy });
  }

  @Post('test-results/videos/:testResultId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload video for a test result (simple, no project/testRun required)' })
  @ApiResponse({ status: 201, description: 'Video uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadSimpleVideo(
    @Param('testResultId') testResultId: string,
    @UploadedFile() file: MulterFile
  ) {
    try {
      if (!file) {
        throw new HttpException('No video file uploaded', HttpStatus.BAD_REQUEST);
      }

      // Store in test-results/videos/{testResultId}
      const url = await this.StorageService.uploadVideoSimple(testResultId, file.buffer, file.mimetype);

      // Update the test_results table with the video URL
      await this.testResultsService.updateTestResultVideoUrl(testResultId, url);

      return { success: true, url };
    } catch (error) {
      console.error(`❌ Failed to upload simple video for result ${testResultId}:`, error);
      throw new HttpException(
        `Failed to upload video: ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('test-results/screenshots/:testResultId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload screenshot for a test result (simple, no project/testRun required)' })
  @ApiResponse({ status: 201, description: 'Screenshot uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadSimpleScreenshot(
    @Param('testResultId') testResultId: string,
    @UploadedFile() file: MulterFile
  ) {
    try {
      if (!file) {
        throw new HttpException('No screenshot file uploaded', HttpStatus.BAD_REQUEST);
      }

      // Store in test-results/screenshots/{testResultId}
      const url = await this.StorageService.uploadScreenshotSimple(testResultId, file.buffer, file.mimetype);

      // Update the test_results table with the screenshot URL
      await this.testResultsService.updateTestResultScreenshotUrl(testResultId, url);

      return { success: true, url };
    } catch (error) {
      console.error(`❌ Failed to upload simple screenshot for result ${testResultId}:`, error);
      throw new HttpException(
        `Failed to upload screenshot: ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('projects/:projectId/test-runs/:testRunId/test-results/:resultId/video')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload video for a test result' })
  @ApiResponse({ status: 201, description: 'Video uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadVideo(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('resultId') resultId: string,
    @UploadedFile() file: MulterFile
  ) {
    try {
      if (!file) {
        throw new HttpException('No video file uploaded', HttpStatus.BAD_REQUEST);
      }
      
      console.log(`📹 Uploading video for result ${resultId} in project ${projectId}, test run ${testRunId}`);
      console.log(`📹 File info: ${file.originalname}, size: ${file.size}, type: ${file.mimetype}`);
      
      // Ensure the test result belongs to the correct project and test run
      const testResult = await this.testResultsService.findByIdAndProjectAndTestRun(resultId, projectId, testRunId);
      if (!testResult) {
        console.error(`❌ Test result not found: ${resultId} for project ${projectId} and test run ${testRunId}`);
        throw new NotFoundException(`Test result not found for project ${projectId} and test run ${testRunId}`);
      }
      
      const testCaseId = testResult?.testCaseId || '';
      const url = await this.StorageService.uploadVideo(
        projectId,
        testResult.testRunId,
        testCaseId,
        resultId,
        file.buffer,
        file.mimetype
      );
      
      await this.testResultsService.updateTestResultVideoUrl(resultId, url);
      
      console.log(`✅ Video uploaded successfully for result ${resultId}: ${url}`);
      return { success: true, url };
    } catch (error) {
      console.error(`❌ Failed to upload video for result ${resultId}:`, error);
      throw new HttpException(
        `Failed to upload video: ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('projects/:projectId/test-runs/:testRunId/test-results/:resultId/screenshot')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload screenshot for a test result' })
  @ApiResponse({ status: 201, description: 'Screenshot uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadScreenshot(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('resultId') resultId: string,
    @UploadedFile() file: MulterFile
  ) {
    try {
      if (!file) {
        throw new HttpException('No screenshot file uploaded', HttpStatus.BAD_REQUEST);
      }
      
      console.log(`📸 Uploading screenshot for result ${resultId} in project ${projectId}, test run ${testRunId}`);
      console.log(`📸 File info: ${file.originalname}, size: ${file.size}, type: ${file.mimetype}`);
      
      // Ensure the test result belongs to the correct project and test run
      const testResult = await this.testResultsService.findByIdAndProjectAndTestRun(resultId, projectId, testRunId);
      if (!testResult) {
        console.error(`❌ Test result not found: ${resultId} for project ${projectId} and test run ${testRunId}`);
        throw new NotFoundException(`Test result not found for project ${projectId} and test run ${testRunId}`);
      }
      
      const testCaseId = testResult?.testCaseId || '';
      const url = await this.StorageService.uploadScreenshot(
        projectId,
        testResult.testRunId,
        testCaseId,
        resultId,
        file.buffer,
        file.mimetype
      );
      
      await this.testResultsService.updateTestResultScreenshotUrl(resultId, url);
      
      console.log(`✅ Screenshot uploaded successfully for result ${resultId}: ${url}`);
      return { success: true, url };
    } catch (error) {
      console.error(`❌ Failed to upload screenshot for result ${resultId}:`, error);
      throw new HttpException(
        `Failed to upload screenshot: ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('projects/:projectId/test-runs/:testRunId/test-results/:resultId/logs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Store logs for a test result' })
  @ApiResponse({ status: 201, description: 'Logs stored successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  async storeLogs(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('resultId') resultId: string,
    @Body() logsData: { projectId: string; tcId: string; logs: string[] }
  ) {
    try {
      const uploadData = {
        projectId: logsData.projectId,
        testCaseId: logsData.tcId,
        tcId: logsData.tcId,
        testRunId: testRunId,
        testResultId: resultId
      };
      const url = await this.StorageService.uploadLogs(uploadData, logsData.logs);
      
      // Update the test result with the logs URL
      await this.testResultsService.updateTestResultLogsUrl(resultId, url);
      
      return { success: true, url };
    } catch (error) {
      throw new HttpException(
        `Failed to store logs: ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch('test-results/:testResultId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update videoUrl and/or screenshotUrl for a test result' })
  @ApiResponse({ status: 200, description: 'Test result updated successfully' })
  async patchTestResult(
    @Param('testResultId') testResultId: string,
    @Body() updateDto: { videoUrl?: string; screenshotUrl?: string },
    @Request() req
  ) {
    const createdBy = req.user?.name || req.user?.email || 'unknown';
    return this.testResultsService.patchTestResult(testResultId, { ...updateDto, createdBy });
  }
}
