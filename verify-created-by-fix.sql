-- SQL queries to verify the created_by fix is working

-- 1. Check recent test results to see if createdBy is being populated
SELECT 
    tr.id,
    tr.status,
    tr."actualResult",
    tr.notes,
    tr."createdBy",
    tr."createdAt",
    tr."isLatest",
    tc."tcId",
    u.name as user_name,
    u.email as user_email
FROM test_results tr
LEFT JOIN test_cases tc ON tr."testCaseId" = tc.id
LEFT JOIN users u ON tr."createdBy" = u.name OR tr."createdBy" = u.email
WHERE tr."createdAt" > NOW() - INTERVAL '1 hour'
ORDER BY tr."createdAt" DESC
LIMIT 20;

-- 2. Check for test results with null createdBy (should be older records)
SELECT 
    COUNT(*) as total_null_created_by,
    <PERSON>N("createdAt") as earliest_null,
    MAX("createdAt") as latest_null
FROM test_results 
WHERE "createdBy" IS NULL;

-- 3. Check for test results with populated createdBy
SELECT 
    COUNT(*) as total_with_created_by,
    MIN("createdAt") as earliest_with_created_by,
    MAX("createdAt") as latest_with_created_by
FROM test_results 
WHERE "createdBy" IS NOT NULL;

-- 4. Show distribution of createdBy values
SELECT 
    "createdBy",
    COUNT(*) as count,
    MIN("createdAt") as first_occurrence,
    MAX("createdAt") as last_occurrence
FROM test_results 
WHERE "createdBy" IS NOT NULL
GROUP BY "createdBy"
ORDER BY count DESC;

-- 5. Check specific test case history to see createdBy progression
-- Replace 'YOUR_TEST_CASE_ID' with an actual test case ID
SELECT 
    tr.id,
    tr.status,
    tr."createdBy",
    tr."createdAt",
    tr.sequence,
    tr."isLatest"
FROM test_results tr
JOIN test_cases tc ON tr."testCaseId" = tc.id
WHERE tc."tcId" = 1  -- Replace with actual tcId
ORDER BY tr.sequence DESC, tr."createdAt" DESC;

-- 6. Check if users table has the expected name/email data
SELECT 
    id,
    name,
    email,
    "createdAt"
FROM users
ORDER BY "createdAt" DESC
LIMIT 10;

-- 7. Verify test results created in the last 24 hours have createdBy
SELECT 
    DATE_TRUNC('hour', "createdAt") as hour,
    COUNT(*) as total_results,
    COUNT("createdBy") as results_with_created_by,
    ROUND(COUNT("createdBy") * 100.0 / COUNT(*), 2) as percentage_with_created_by
FROM test_results 
WHERE "createdAt" > NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', "createdAt")
ORDER BY hour DESC;

-- 8. Check for any test results created by 'system' (from helper methods)
SELECT 
    COUNT(*) as system_created_count,
    MIN("createdAt") as earliest_system,
    MAX("createdAt") as latest_system
FROM test_results 
WHERE "createdBy" = 'system';

-- 9. Sample of recent test results with all relevant fields
SELECT 
    tr.id,
    tc."tcId",
    tr.status,
    tr."createdBy",
    tr."createdAt",
    tr."isLatest",
    tr.sequence,
    CASE 
        WHEN tr."createdBy" IS NULL THEN 'Missing createdBy'
        WHEN tr."createdBy" = 'system' THEN 'System generated'
        WHEN tr."createdBy" = 'unknown' THEN 'User lookup failed'
        ELSE 'User identified'
    END as created_by_status
FROM test_results tr
JOIN test_cases tc ON tr."testCaseId" = tc.id
WHERE tr."createdAt" > NOW() - INTERVAL '2 hours'
ORDER BY tr."createdAt" DESC
LIMIT 15;
